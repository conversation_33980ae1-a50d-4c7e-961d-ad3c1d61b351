import axios from 'axios';
import useAuthStore from '../store/authStore';
import { message } from 'antd';

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const { token } = useAuthStore.getState();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const { response } = error;
    const { logout, refreshToken, updateToken } = useAuthStore.getState();
    
    if (response?.status === 401) {
      // Token过期，尝试刷新
      if (refreshToken) {
        try {
          const refreshResponse = await axios.post('/api/auth/refresh', {}, {
            headers: {
              Authorization: `Bearer ${refreshToken}`,
            },
          });
          
          const { access_token } = refreshResponse.data;
          updateToken(access_token);
          
          // 重新发送原请求
          error.config.headers.Authorization = `Bearer ${access_token}`;
          return api.request(error.config);
        } catch (refreshError) {
          // 刷新失败，登出用户
          logout();
          window.location.href = '/login';
          message.error('登录已过期，请重新登录');
        }
      } else {
        logout();
        window.location.href = '/login';
        message.error('请先登录');
      }
    } else if (response?.status >= 500) {
      message.error('服务器错误，请稍后重试');
    } else if (response?.data?.error) {
      message.error(response.data.error);
    }
    
    return Promise.reject(error);
  }
);

// 认证相关API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  refresh: () => api.post('/auth/refresh'),
  logout: () => api.post('/auth/logout'),
  getProfile: () => api.get('/auth/profile'),
  updateProfile: (userData) => api.put('/auth/profile', userData),
  changePassword: (passwordData) => api.post('/auth/change-password', passwordData),
};

// 视频相关API
export const videoAPI = {
  validateUrls: (urls) => api.post('/videos/validate', { urls }),
  submitVideos: (urls) => api.post('/videos/submit', { urls }),
  getVideos: (params) => api.get('/videos/list', { params }),
  getVideo: (id) => api.get(`/videos/${id}`),
  deleteVideo: (id) => api.delete(`/videos/${id}`),
  reprocessVideo: (id) => api.post(`/videos/${id}/reprocess`),
  getTaskStatus: (taskId) => api.get(`/videos/task/${taskId}/status`),
  getStatistics: () => api.get('/videos/statistics'),
};

// 导出相关API
export const exportAPI = {
  exportExcel: (videoIds) => api.post('/exports/excel', { video_ids: videoIds }),
  syncNotion: (videoIds) => api.post('/exports/notion', { video_ids: videoIds }),
  sendEmail: (videoIds, recipients, includeExcel = true) => 
    api.post('/exports/email', { 
      video_ids: videoIds, 
      recipients, 
      include_excel: includeExcel 
    }),
  getExportRecords: (params) => api.get('/exports/records', { params }),
  getExportStatus: (exportId) => api.get(`/exports/records/${exportId}/status`),
  downloadFile: (filename) => api.get(`/exports/download/${filename}`, { 
    responseType: 'blob' 
  }),
  deleteExportRecord: (exportId) => api.delete(`/exports/records/${exportId}`),
};

export default api;
