import React, { useState } from 'react';
import { Layout, Menu } from 'antd';
import {
  DashboardOutlined,
  PlusOutlined,
  VideoCameraOutlined,
  ExportOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';

const { Sider } = Layout;

const AppSider = () => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: '/submit',
      icon: <PlusOutlined />,
      label: '提交视频',
    },
    {
      key: '/videos',
      icon: <VideoCameraOutlined />,
      label: '视频管理',
    },
    {
      key: '/exports',
      icon: <ExportOutlined />,
      label: '导出记录',
    },
    {
      key: '/profile',
      icon: <UserOutlined />,
      label: '个人设置',
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  return (
    <Sider
      className="app-sider"
      collapsible
      collapsed={collapsed}
      onCollapse={setCollapsed}
    >
      <div className="logo">
        {collapsed ? '抖音' : '抖音视频提取'}
      </div>
      
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
      />
    </Sider>
  );
};

export default AppSider;
