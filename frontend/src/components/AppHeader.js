import React from 'react';
import { Layout, Dropdown, Avatar, Space, Typography, Button } from 'antd';
import { UserOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '../store/authStore';

const { Header } = Layout;
const { Text } = Typography;

const AppHeader = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <SettingOutlined />,
      label: '个人设置',
      onClick: () => navigate('/profile'),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <Header className="app-header">
      <div className="header-left">
        <Text strong style={{ fontSize: 16 }}>
          抖音视频文本提取系统
        </Text>
      </div>
      
      <div className="header-right">
        <Dropdown
          menu={{ items: userMenuItems }}
          placement="bottomRight"
          trigger={['click']}
        >
          <div className="user-info">
            <Avatar
              size="small"
              icon={<UserOutlined />}
              src={user?.avatar_url}
              style={{ backgroundColor: '#1890ff' }}
            />
            <Space>
              <Text>{user?.full_name || user?.username}</Text>
            </Space>
          </div>
        </Dropdown>
      </div>
    </Header>
  );
};

export default AppHeader;
