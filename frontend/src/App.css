.app-container {
  min-height: 100vh;
}

/* 侧边栏样式 */
.app-sider {
  background: #001529;
}

.app-sider .ant-layout-sider-trigger {
  background: #002140;
}

.logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: bold;
  font-size: 16px;
}

/* 头部样式 */
.app-header {
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: bold;
}

/* 主内容区域 */
.main-content {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

/* 页面容器 */
.page-container {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-description {
  color: #8c8c8c;
  margin: 0;
  font-size: 14px;
}

/* 统计卡片 */
.stats-card {
  text-align: center;
  padding: 24px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.stats-card.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-card.warning {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stats-card.error {
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 状态标签 */
.status-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  font-weight: 500;
}

.status-pending {
  background: #fff7e6;
  color: #d46b08;
  border: 1px solid #ffd591;
}

.status-processing {
  background: #e6f7ff;
  color: #0958d9;
  border: 1px solid #91d5ff;
}

.status-completed {
  background: #f6ffed;
  color: #389e0d;
  border: 1px solid #b7eb8f;
}

.status-failed {
  background: #fff2f0;
  color: #cf1322;
  border: 1px solid #ffccc7;
}

.status-cancelled {
  background: #f5f5f5;
  color: #595959;
  border: 1px solid #d9d9d9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }
  
  .page-container {
    padding: 16px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .header-right {
    gap: 8px;
  }
  
  .user-info {
    padding: 4px 8px;
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: #8c8c8c;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

.empty-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #595959;
}

.empty-description {
  font-size: 14px;
  color: #8c8c8c;
}
