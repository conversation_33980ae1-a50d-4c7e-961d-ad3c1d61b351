import React from 'react';
import { Row, Col, Card, Statistic, List, Button, Typography, Space, Tag } from 'antd';
import { 
  VideoCameraOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined, 
  ExclamationCircleOutlined,
  PlusOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import { videoAPI } from '../services/api';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

const DashboardPage = () => {
  const navigate = useNavigate();

  const { data: statistics, isLoading: statsLoading } = useQuery(
    'video-statistics',
    videoAPI.getStatistics,
    {
      select: (response) => response.data,
    }
  );

  const getStatusColor = (status) => {
    const colors = {
      pending: 'orange',
      processing: 'blue',
      completed: 'green',
      failed: 'red',
      cancelled: 'default',
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      pending: '等待处理',
      processing: '处理中',
      completed: '已完成',
      failed: '处理失败',
      cancelled: '已取消',
    };
    return texts[status] || status;
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <Title level={2} className="page-title">
          仪表盘
        </Title>
        <Text className="page-description">
          查看您的视频处理统计和最近活动
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stats-card">
            <Statistic
              title="总视频数"
              value={statistics?.total_count || 0}
              prefix={<VideoCameraOutlined />}
              valueStyle={{ color: '#fff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stats-card success">
            <Statistic
              title="处理完成"
              value={statistics?.statistics?.completed || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#fff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stats-card warning">
            <Statistic
              title="处理中"
              value={statistics?.statistics?.processing || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stats-card error">
            <Statistic
              title="处理失败"
              value={statistics?.statistics?.failed || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#fff' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 快速操作 */}
        <Col xs={24} lg={8}>
          <Card
            title="快速操作"
            extra={<Button type="link" onClick={() => navigate('/submit')}>更多</Button>}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                block
                onClick={() => navigate('/submit')}
              >
                提交新视频
              </Button>
              <Button
                icon={<VideoCameraOutlined />}
                block
                onClick={() => navigate('/videos')}
              >
                查看所有视频
              </Button>
              <Button
                icon={<EyeOutlined />}
                block
                onClick={() => navigate('/exports')}
              >
                导出记录
              </Button>
            </Space>
          </Card>
        </Col>

        {/* 最近视频 */}
        <Col xs={24} lg={16}>
          <Card
            title="最近视频"
            extra={<Button type="link" onClick={() => navigate('/videos')}>查看全部</Button>}
          >
            {statistics?.recent_videos?.length > 0 ? (
              <List
                dataSource={statistics.recent_videos}
                renderItem={(video) => (
                  <List.Item
                    actions={[
                      <Button
                        type="link"
                        onClick={() => navigate(`/videos/${video.id}`)}
                      >
                        查看详情
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          <Text strong>{video.title}</Text>
                          <Tag color={getStatusColor(video.status)}>
                            {getStatusText(video.status)}
                          </Tag>
                        </Space>
                      }
                      description={
                        <Space direction="vertical" size={4}>
                          <Text type="secondary">
                            视频ID: {video.video_id}
                          </Text>
                          <Text type="secondary">
                            创建时间: {dayjs(video.created_at).format('YYYY-MM-DD HH:mm')}
                          </Text>
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <div className="empty-state">
                <VideoCameraOutlined className="empty-icon" />
                <div className="empty-title">暂无视频</div>
                <div className="empty-description">
                  点击"提交新视频"开始使用系统
                </div>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  style={{ marginTop: 16 }}
                  onClick={() => navigate('/submit')}
                >
                  提交新视频
                </Button>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardPage;
