import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useAuthStore = create(
  persist(
    (set, get) => ({
      // 状态
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      
      // 动作
      login: (userData, accessToken, refreshToken) => {
        set({
          user: userData,
          token: accessToken,
          refreshToken: refreshToken,
          isAuthenticated: true,
        });
      },
      
      logout: () => {
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
        });
        // 清除本地存储
        localStorage.removeItem('auth-storage');
      },
      
      updateUser: (userData) => {
        set((state) => ({
          user: { ...state.user, ...userData },
        }));
      },
      
      updateToken: (newToken) => {
        set({ token: newToken });
      },
      
      // 检查token是否过期
      isTokenExpired: () => {
        const { token } = get();
        if (!token) return true;
        
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const currentTime = Date.now() / 1000;
          return payload.exp < currentTime;
        } catch (error) {
          return true;
        }
      },
      
      // 获取用户权限
      hasPermission: (permission) => {
        const { user } = get();
        if (!user) return false;
        
        // 管理员拥有所有权限
        if (user.is_admin) return true;
        
        // 这里可以根据实际需求扩展权限检查逻辑
        return true;
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

export default useAuthStore;
