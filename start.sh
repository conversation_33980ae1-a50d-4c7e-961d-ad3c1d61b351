#!/bin/bash

# 抖音视频文本提取系统 - 快速启动脚本

set -e

echo "🚀 抖音视频文本提取系统 - 快速启动"
echo "=================================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查.env文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
    echo "✅ 已创建.env文件，请根据需要修改配置"
fi

# 构建和启动服务
echo "🔨 构建Docker镜像..."
docker-compose build

echo "🚀 启动服务..."
docker-compose up -d

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 10

# 初始化数据库
echo "🗄️ 初始化数据库..."
docker-compose exec backend python init_db.py all

echo ""
echo "✅ 系统启动完成！"
echo ""
echo "📱 访问地址："
echo "   前端应用: http://localhost:3000"
echo "   后端API:  http://localhost:5000"
echo "   Nginx:    http://localhost:80"
echo ""
echo "🗄️ 数据库信息："
echo "   PostgreSQL: localhost:5432"
echo "   数据库名:   quickvidnote_extract"
echo "   用户名:     quickvidnote_user"
echo "   密码:       Y8mNkxKr10TEJz"
echo ""
echo "👤 默认管理员账户："
echo "   用户名: admin"
echo "   密码:   admin123"
echo "   ⚠️  请在生产环境中修改默认密码！"
echo ""
echo "📋 常用命令："
echo "   查看日志:     docker-compose logs -f"
echo "   停止服务:     docker-compose down"
echo "   重启服务:     docker-compose restart"
echo "   查看状态:     docker-compose ps"
echo ""
echo "📚 更多信息请查看 README.md 和 DATABASE_MIGRATION.md"
