#!/usr/bin/env python3
"""
数据库初始化脚本
用于创建数据库表和初始数据
"""

import os
import sys
from flask_migrate import init, migrate, upgrade
from app import create_app, db
from app.models import User, Video, VideoProcessingTask, ExportRecord

def init_database():
    """初始化数据库"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查是否已经初始化过迁移
            migrations_dir = os.path.join(os.path.dirname(__file__), 'migrations')
            
            if not os.path.exists(migrations_dir):
                print("初始化数据库迁移...")
                init()
            
            # 创建迁移文件
            print("生成迁移文件...")
            migrate(message='Initial migration')
            
            # 应用迁移
            print("应用数据库迁移...")
            upgrade()
            
            print("数据库初始化完成！")
            
        except Exception as e:
            print(f"数据库初始化失败: {str(e)}")
            sys.exit(1)

def create_admin_user():
    """创建管理员用户"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查是否已存在管理员用户
            admin = User.query.filter_by(username='admin').first()
            if admin:
                print("管理员用户已存在")
                return
            
            # 创建管理员用户
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='系统管理员',
                is_admin=True
            )
            admin.set_password('admin123')  # 请在生产环境中修改密码
            
            db.session.add(admin)
            db.session.commit()
            
            print("管理员用户创建成功！")
            print("用户名: admin")
            print("密码: admin123")
            print("请在生产环境中修改默认密码！")
            
        except Exception as e:
            print(f"创建管理员用户失败: {str(e)}")
            db.session.rollback()

if __name__ == '__main__':
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == 'init':
            init_database()
        elif command == 'admin':
            create_admin_user()
        elif command == 'all':
            init_database()
            create_admin_user()
        else:
            print("用法:")
            print("  python init_db.py init    # 初始化数据库")
            print("  python init_db.py admin   # 创建管理员用户")
            print("  python init_db.py all     # 初始化数据库并创建管理员用户")
    else:
        print("用法:")
        print("  python init_db.py init    # 初始化数据库")
        print("  python init_db.py admin   # 创建管理员用户")
        print("  python init_db.py all     # 初始化数据库并创建管理员用户")
