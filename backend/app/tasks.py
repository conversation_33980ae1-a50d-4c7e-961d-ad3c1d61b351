from celery import current_task
from app import db, celery
from app.models import Video, VideoProcessingTask, ProcessingStatus
from app.services import DouyinService, SpeechService
import traceback

@celery.task(bind=True)
def process_video_task(self, video_id: int):
    """处理单个视频的异步任务"""
    task_id = self.request.id
    
    # 创建任务记录
    task_record = VideoProcessingTask(
        video_id=video_id,
        task_id=task_id,
        task_name='process_video'
    )
    db.session.add(task_record)
    db.session.commit()
    
    try:
        # 获取视频记录
        video = Video.query.get(video_id)
        if not video:
            raise Exception(f"视频记录不存在: {video_id}")
        
        # 更新任务状态
        task_record.status = 'PROCESSING'
        task_record.current_step = '开始处理视频'
        task_record.progress = 10
        db.session.commit()
        
        # 更新视频状态
        video.start_processing()
        
        # 步骤1: 解析视频信息
        self.update_state(
            state='PROGRESS',
            meta={'current': 20, 'total': 100, 'status': '解析视频信息...'}
        )
        task_record.current_step = '解析视频信息'
        task_record.progress = 20
        db.session.commit()
        
        douyin_service = DouyinService()
        
        # 如果还没有视频URL，重新解析
        if not video.video_url:
            video_info = douyin_service.parse_share_url(video.original_url)
            video.video_url = video_info.get('video_url')
            video.title = video_info.get('title', video.title)
            video.description = video_info.get('description', video.description)
            db.session.commit()
        
        if not video.video_url:
            raise Exception("无法获取视频播放地址")
        
        # 步骤2: 语音识别
        self.update_state(
            state='PROGRESS',
            meta={'current': 50, 'total': 100, 'status': '正在进行语音识别...'}
        )
        task_record.current_step = '语音识别处理'
        task_record.progress = 50
        db.session.commit()
        
        speech_service = SpeechService()
        recognition_result = speech_service.process_video_transcription(video.video_url)
        
        # 步骤3: 保存结果
        self.update_state(
            state='PROGRESS',
            meta={'current': 90, 'total': 100, 'status': '保存识别结果...'}
        )
        task_record.current_step = '保存识别结果'
        task_record.progress = 90
        db.session.commit()
        
        if recognition_result['success']:
            # 处理成功
            video.complete_processing(
                transcription_text=recognition_result['text'],
                confidence=recognition_result['confidence']
            )
            
            task_record.status = 'SUCCESS'
            task_record.result = recognition_result['text']
            task_record.progress = 100
            task_record.current_step = '处理完成'
            
        else:
            # 识别失败
            error_msg = recognition_result.get('error', '语音识别失败')
            video.fail_processing(error_msg)
            
            task_record.status = 'FAILURE'
            task_record.error_info = error_msg
            task_record.current_step = '处理失败'
        
        db.session.commit()
        
        return {
            'status': 'completed',
            'video_id': video_id,
            'transcription': recognition_result['text'] if recognition_result['success'] else None,
            'confidence': recognition_result['confidence'] if recognition_result['success'] else None,
            'error': recognition_result.get('error') if not recognition_result['success'] else None
        }
        
    except Exception as e:
        # 处理异常
        error_msg = str(e)
        traceback_str = traceback.format_exc()
        
        # 更新视频状态
        if 'video' in locals():
            video.fail_processing(error_msg)
        
        # 更新任务状态
        task_record.status = 'FAILURE'
        task_record.error_info = f"{error_msg}\n\n{traceback_str}"
        task_record.current_step = '处理失败'
        db.session.commit()
        
        # 抛出异常给Celery
        self.update_state(
            state='FAILURE',
            meta={'current': 100, 'total': 100, 'status': f'处理失败: {error_msg}'}
        )
        
        raise Exception(error_msg)

@celery.task(bind=True)
def batch_process_videos_task(self, video_ids: list):
    """批量处理视频的异步任务"""
    task_id = self.request.id
    total_videos = len(video_ids)
    processed_count = 0
    results = []
    
    try:
        for i, video_id in enumerate(video_ids):
            # 更新总体进度
            progress = int((i / total_videos) * 100)
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': progress,
                    'total': 100,
                    'status': f'处理视频 {i+1}/{total_videos}',
                    'processed': processed_count,
                    'total_videos': total_videos
                }
            )
            
            try:
                # 调用单个视频处理任务
                result = process_video_task.apply(args=[video_id])
                results.append({
                    'video_id': video_id,
                    'status': 'success',
                    'result': result.get()
                })
                processed_count += 1
                
            except Exception as e:
                results.append({
                    'video_id': video_id,
                    'status': 'error',
                    'error': str(e)
                })
        
        return {
            'status': 'completed',
            'total_videos': total_videos,
            'processed_count': processed_count,
            'results': results
        }
        
    except Exception as e:
        self.update_state(
            state='FAILURE',
            meta={'status': f'批量处理失败: {str(e)}'}
        )
        raise Exception(str(e))

@celery.task(bind=True)
def export_task(self, export_id: int):
    """导出任务"""
    from app.models import ExportRecord, ExportType, Video, User
    from app.services import ExportService
    import json

    try:
        # 获取导出记录
        export_record = ExportRecord.query.get(export_id)
        if not export_record:
            raise Exception(f"导出记录不存在: {export_id}")

        # 获取用户和视频数据
        user = User.query.get(export_record.user_id)
        video_ids = json.loads(export_record.video_ids)
        videos = Video.query.filter(Video.id.in_(video_ids)).all()

        export_service = ExportService()

        if export_record.export_type == ExportType.EXCEL:
            # Excel导出
            self.update_state(
                state='PROGRESS',
                meta={'current': 50, 'total': 100, 'status': '生成Excel文件...'}
            )

            file_path = export_service.export_to_excel(videos)
            file_url = export_service.get_export_file_url(file_path)

            export_record.complete_export(
                file_path=file_path,
                file_url=file_url
            )

            return {'status': 'completed', 'file_url': file_url}

        elif export_record.export_type == ExportType.NOTION:
            # Notion同步
            self.update_state(
                state='PROGRESS',
                meta={'current': 50, 'total': 100, 'status': '同步到Notion...'}
            )

            result = export_service.sync_to_notion(videos, user)
            export_record.complete_export()

            return {'status': 'completed', 'message': result}

        elif export_record.export_type == ExportType.EMAIL:
            # 邮件发送
            config = json.loads(export_record.export_config or '{}')
            recipients = json.loads(export_record.email_recipients)

            excel_file = None
            if config.get('include_excel', True):
                self.update_state(
                    state='PROGRESS',
                    meta={'current': 30, 'total': 100, 'status': '生成Excel文件...'}
                )
                excel_file = export_service.export_to_excel(videos)

            self.update_state(
                state='PROGRESS',
                meta={'current': 70, 'total': 100, 'status': '发送邮件...'}
            )

            result = export_service.send_email(videos, user, recipients, excel_file)
            export_record.complete_export()

            return {'status': 'completed', 'message': result}

    except Exception as e:
        export_record.fail_export(str(e))
        self.update_state(
            state='FAILURE',
            meta={'status': f'导出失败: {str(e)}'}
        )
        raise Exception(str(e))

@celery.task
def cleanup_old_tasks():
    """清理旧的任务记录"""
    from datetime import datetime, timedelta

    # 删除7天前的已完成任务记录
    cutoff_date = datetime.utcnow() - timedelta(days=7)
    old_tasks = VideoProcessingTask.query.filter(
        VideoProcessingTask.completed_at < cutoff_date,
        VideoProcessingTask.status.in_(['SUCCESS', 'FAILURE'])
    ).all()

    for task in old_tasks:
        db.session.delete(task)

    db.session.commit()
    return f"清理了 {len(old_tasks)} 个旧任务记录"
