import os
import json
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any
from flask import current_app
from flask_mail import Message
from notion_client import Client
from app import mail
from app.models import Video, User, ExportRecord, ExportType
from app.config import Config

class ExportService:
    """导出服务"""
    
    def __init__(self):
        self.upload_folder = Config.UPLOAD_FOLDER
        if not os.path.exists(self.upload_folder):
            os.makedirs(self.upload_folder)
    
    def export_to_excel(self, videos: List[Video], filename: str = None) -> str:
        """导出视频数据到Excel"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'douyin_videos_{timestamp}.xlsx'
        
        filepath = os.path.join(self.upload_folder, filename)
        
        # 准备数据
        data = []
        for video in videos:
            data.append({
                '视频ID': video.video_id,
                '标题': video.title,
                '描述': video.description or '',
                '原始链接': video.original_url,
                '视频链接': video.video_url or '',
                '处理状态': video.status.value,
                '转录文本': video.transcription_text or '',
                '识别置信度': video.transcription_confidence or 0,
                '创建时间': video.created_at.strftime('%Y-%m-%d %H:%M:%S') if video.created_at else '',
                '处理开始时间': video.processing_started_at.strftime('%Y-%m-%d %H:%M:%S') if video.processing_started_at else '',
                '处理完成时间': video.processing_completed_at.strftime('%Y-%m-%d %H:%M:%S') if video.processing_completed_at else '',
                '处理耗时(秒)': video.processing_duration or 0,
                '错误信息': video.error_message or ''
            })
        
        # 创建DataFrame并导出
        df = pd.DataFrame(data)
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='视频数据', index=False)
            
            # 设置列宽
            worksheet = writer.sheets['视频数据']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        return filepath
    
    def sync_to_notion(self, videos: List[Video], user: User) -> str:
        """同步数据到Notion"""
        if not user.notion_token or not user.notion_database_id:
            raise ValueError("用户未配置Notion集成信息")
        
        notion = Client(auth=user.notion_token)
        
        try:
            # 验证数据库是否存在
            database = notion.databases.retrieve(database_id=user.notion_database_id)
        except Exception as e:
            raise ValueError(f"无法访问Notion数据库: {str(e)}")
        
        created_pages = []
        
        for video in videos:
            try:
                # 创建页面属性
                properties = {
                    "标题": {
                        "title": [
                            {
                                "text": {
                                    "content": video.title[:100]  # Notion标题限制
                                }
                            }
                        ]
                    },
                    "视频ID": {
                        "rich_text": [
                            {
                                "text": {
                                    "content": video.video_id
                                }
                            }
                        ]
                    },
                    "原始链接": {
                        "url": video.original_url
                    },
                    "处理状态": {
                        "select": {
                            "name": video.status.value
                        }
                    },
                    "创建时间": {
                        "date": {
                            "start": video.created_at.isoformat() if video.created_at else None
                        }
                    }
                }
                
                # 如果有转录文本，添加到页面内容
                children = []
                if video.transcription_text:
                    children.append({
                        "object": "block",
                        "type": "paragraph",
                        "paragraph": {
                            "rich_text": [
                                {
                                    "type": "text",
                                    "text": {
                                        "content": "转录文本："
                                    },
                                    "annotations": {
                                        "bold": True
                                    }
                                }
                            ]
                        }
                    })
                    
                    # 分段添加转录文本（Notion有字符限制）
                    text_chunks = [video.transcription_text[i:i+2000] 
                                 for i in range(0, len(video.transcription_text), 2000)]
                    
                    for chunk in text_chunks:
                        children.append({
                            "object": "block",
                            "type": "paragraph",
                            "paragraph": {
                                "rich_text": [
                                    {
                                        "type": "text",
                                        "text": {
                                            "content": chunk
                                        }
                                    }
                                ]
                            }
                        })
                
                # 创建页面
                page = notion.pages.create(
                    parent={"database_id": user.notion_database_id},
                    properties=properties,
                    children=children
                )
                
                created_pages.append(page['url'])
                
            except Exception as e:
                print(f"创建Notion页面失败 (视频ID: {video.video_id}): {str(e)}")
                continue
        
        return f"成功同步 {len(created_pages)} 个视频到Notion"
    
    def send_email(self, videos: List[Video], user: User, recipients: List[str], 
                   excel_file: str = None) -> str:
        """发送邮件"""
        if not recipients:
            raise ValueError("请提供收件人邮箱")
        
        # 创建邮件
        subject = f"抖音视频转录结果 - {datetime.now().strftime('%Y-%m-%d')}"
        
        # 邮件正文
        body = f"""
        您好，
        
        这是您请求的抖音视频转录结果，共包含 {len(videos)} 个视频。
        
        处理统计：
        """
        
        # 统计各状态数量
        status_counts = {}
        for video in videos:
            status = video.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        for status, count in status_counts.items():
            body += f"- {status}: {count} 个\n"
        
        body += f"""
        
        如果您有任何问题，请联系我们。
        
        此邮件由系统自动发送，请勿回复。
        """
        
        msg = Message(
            subject=subject,
            recipients=recipients,
            body=body,
            sender=Config.MAIL_DEFAULT_SENDER
        )
        
        # 如果有Excel文件，添加为附件
        if excel_file and os.path.exists(excel_file):
            with current_app.open_resource(excel_file) as fp:
                msg.attach(
                    filename=os.path.basename(excel_file),
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    data=fp.read()
                )
        
        # 发送邮件
        mail.send(msg)
        
        return f"邮件已发送给 {len(recipients)} 个收件人"
    
    def get_export_file_url(self, filepath: str) -> str:
        """获取导出文件的下载链接"""
        filename = os.path.basename(filepath)
        # 这里应该返回实际的文件下载URL
        # 在生产环境中，可能需要配置静态文件服务或云存储
        return f"/api/exports/download/{filename}"
