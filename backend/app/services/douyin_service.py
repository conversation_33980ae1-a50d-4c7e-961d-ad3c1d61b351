import re
import json
import requests
from urllib.parse import urlparse
from app.config import Config

class DouyinService:
    """抖音视频处理服务"""
    
    def __init__(self):
        self.headers = Config.DOUYIN_HEADERS
    
    def validate_douyin_url(self, url: str) -> bool:
        """验证抖音链接格式"""
        douyin_patterns = [
            r'https?://v\.douyin\.com/\w+',
            r'https?://www\.douyin\.com/video/\d+',
            r'https?://www\.iesdouyin\.com/share/video/\d+',
            r'https?://douyin\.com/video/\d+',
        ]
        
        for pattern in douyin_patterns:
            if re.match(pattern, url):
                return True
        return False
    
    def extract_urls_from_text(self, text: str) -> list:
        """从文本中提取所有URL"""
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        urls = re.findall(url_pattern, text)
        
        # 过滤出抖音链接
        douyin_urls = []
        for url in urls:
            if self.validate_douyin_url(url):
                douyin_urls.append(url)
        
        return douyin_urls
    
    def parse_share_url(self, share_text: str) -> dict:
        """从分享文本中提取无水印视频链接"""
        try:
            # 提取分享链接
            urls = self.extract_urls_from_text(share_text)
            if not urls:
                raise ValueError("未找到有效的分享链接")
            
            share_url = urls[0]
            
            # 获取重定向后的真实链接
            response = requests.get(share_url, headers=self.headers, allow_redirects=True, timeout=10)
            response.raise_for_status()
            
            # 从URL中提取视频ID
            video_id = self._extract_video_id(response.url)
            if not video_id:
                raise ValueError("无法提取视频ID")
            
            # 构建标准的分享链接
            standard_url = f'https://www.iesdouyin.com/share/video/{video_id}'
            
            # 获取视频页面内容
            page_response = requests.get(standard_url, headers=self.headers, timeout=10)
            page_response.raise_for_status()
            
            # 解析页面中的视频信息
            video_info = self._parse_video_info(page_response.text, video_id)
            
            return video_info
            
        except requests.RequestException as e:
            raise ValueError(f"网络请求失败: {str(e)}")
        except Exception as e:
            raise ValueError(f"解析视频信息失败: {str(e)}")
    
    def _extract_video_id(self, url: str) -> str:
        """从URL中提取视频ID"""
        # 尝试多种模式提取视频ID
        patterns = [
            r'/video/(\d+)',
            r'/share/video/(\d+)',
            r'modal_id=(\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        # 如果URL中包含问号，去掉参数部分再尝试
        clean_url = url.split("?")[0].strip("/")
        video_id = clean_url.split("/")[-1]
        
        # 验证是否为数字ID
        if video_id.isdigit():
            return video_id
        
        return None
    
    def _parse_video_info(self, html_content: str, video_id: str) -> dict:
        """解析HTML页面中的视频信息"""
        # 查找包含视频数据的JavaScript代码
        pattern = re.compile(
            pattern=r"window\._ROUTER_DATA\s*=\s*(.*?)</script>",
            flags=re.DOTALL,
        )
        
        match = pattern.search(html_content)
        if not match or not match.group(1):
            raise ValueError("从HTML中解析视频信息失败")
        
        try:
            # 解析JSON数据
            json_data = json.loads(match.group(1).strip())
            
            # 尝试不同的数据路径
            video_data = None
            possible_keys = [
                "video_(id)/page",
                "note_(id)/page",
                f"video_{video_id}/page",
                f"note_{video_id}/page"
            ]
            
            for key in possible_keys:
                if key in json_data.get("loaderData", {}):
                    video_data = json_data["loaderData"][key].get("videoInfoRes")
                    break
            
            if not video_data or "item_list" not in video_data:
                raise ValueError("无法从JSON中解析视频信息")
            
            # 提取视频详细信息
            item = video_data["item_list"][0]
            
            # 获取视频播放地址（去水印）
            video_url = None
            if "video" in item and "play_addr" in item["video"]:
                play_addr = item["video"]["play_addr"]
                if "url_list" in play_addr and play_addr["url_list"]:
                    video_url = play_addr["url_list"][0].replace("playwm", "play")
            
            # 获取视频标题和描述
            title = item.get("desc", "").strip()
            if not title:
                title = f"douyin_{video_id}"
            
            # 清理标题中的非法字符
            title = re.sub(r'[\\/:*?"<>|]', '_', title)
            
            # 获取作者信息
            author_info = {}
            if "author" in item:
                author = item["author"]
                author_info = {
                    "nickname": author.get("nickname", ""),
                    "unique_id": author.get("unique_id", ""),
                    "avatar_url": author.get("avatar_thumb", {}).get("url_list", [""])[0] if author.get("avatar_thumb") else ""
                }
            
            # 获取视频统计信息
            statistics = {}
            if "statistics" in item:
                stats = item["statistics"]
                statistics = {
                    "digg_count": stats.get("digg_count", 0),  # 点赞数
                    "comment_count": stats.get("comment_count", 0),  # 评论数
                    "share_count": stats.get("share_count", 0),  # 分享数
                    "play_count": stats.get("play_count", 0)  # 播放数
                }
            
            # 获取视频时长
            duration = 0
            if "video" in item and "duration" in item["video"]:
                duration = item["video"]["duration"] / 1000  # 转换为秒
            
            return {
                "video_id": video_id,
                "title": title,
                "description": item.get("desc", ""),
                "video_url": video_url,
                "duration": duration,
                "author": author_info,
                "statistics": statistics,
                "create_time": item.get("create_time", 0)
            }
            
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON解析失败: {str(e)}")
        except KeyError as e:
            raise ValueError(f"视频数据结构异常: {str(e)}")
    
    def batch_parse_urls(self, urls: list) -> list:
        """批量解析视频链接"""
        results = []
        
        for url in urls:
            try:
                video_info = self.parse_share_url(url)
                video_info['original_url'] = url
                video_info['status'] = 'success'
                results.append(video_info)
            except Exception as e:
                results.append({
                    'original_url': url,
                    'status': 'error',
                    'error_message': str(e)
                })
        
        return results
