import os
import json
import time
import requests
import tempfile
from typing import Optional, Dict, Any
from http import HTTPStatus
import dashscope
from dashscope.audio.asr import Transcription
from app.config import Config

class SpeechService:
    """语音识别服务"""
    
    def __init__(self):
        # 设置阿里云API密钥
        dashscope.api_key = Config.ALIYUN_ACCESS_KEY_ID
    
    def download_video(self, video_url: str) -> str:
        """下载视频文件到临时目录"""
        try:
            response = requests.get(video_url, headers=Config.DOUYIN_HEADERS, stream=True, timeout=30)
            response.raise_for_status()
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as temp_file:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        temp_file.write(chunk)
                return temp_file.name
                
        except Exception as e:
            raise Exception(f"下载视频失败: {str(e)}")
    
    def extract_audio_from_video(self, video_path: str) -> str:
        """从视频中提取音频（需要ffmpeg）"""
        try:
            import subprocess
            
            # 创建临时音频文件
            audio_path = video_path.replace('.mp4', '.wav')
            
            # 使用ffmpeg提取音频
            cmd = [
                'ffmpeg', '-i', video_path,
                '-vn',  # 不处理视频
                '-acodec', 'pcm_s16le',  # 音频编码
                '-ar', '16000',  # 采样率
                '-ac', '1',  # 单声道
                '-y',  # 覆盖输出文件
                audio_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"音频提取失败: {result.stderr}")
            
            return audio_path
            
        except FileNotFoundError:
            raise Exception("ffmpeg未安装，请先安装ffmpeg")
        except Exception as e:
            raise Exception(f"音频提取失败: {str(e)}")
    
    def upload_file_to_public_url(self, file_path: str) -> str:
        """将本地文件上传到公网可访问的URL
        注意：这里需要根据实际情况实现文件上传逻辑
        可以上传到阿里云OSS、腾讯云COS等对象存储服务
        """
        # TODO: 实现文件上传逻辑
        # 这里返回一个示例URL，实际使用时需要替换为真实的上传逻辑
        raise NotImplementedError("需要实现文件上传到公网URL的逻辑")

    def recognize_speech_from_file(self, audio_file_path: str) -> Dict[str, Any]:
        """使用阿里云语音识别API识别音频文件"""
        try:
            # 由于API只支持公网URL，需要先上传文件
            # 在实际使用中，应该将文件上传到OSS等对象存储服务
            return {
                'success': False,
                'error': '本地文件识别需要先上传到公网可访问的URL',
                'text': '',
                'confidence': 0
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'语音识别失败: {str(e)}',
                'text': '',
                'confidence': 0
            }
    
    def recognize_speech_from_url(self, audio_url: str) -> Dict[str, Any]:
        """直接从URL识别语音"""
        try:
            recognition = SpeechRecognizer.call(
                model='paraformer-realtime-v1',
                format='mp4',  # 支持视频格式
                sample_rate=16000,
                callback=None,
                file_urls=[audio_url]
            )
            
            if recognition.status_code == 200:
                result = recognition.output
                
                if 'results' in result and result['results']:
                    transcription = ""
                    confidence_scores = []
                    
                    for item in result['results']:
                        if 'transcripts' in item:
                            for transcript in item['transcripts']:
                                transcription += transcript.get('text', '')
                                if 'confidence' in transcript:
                                    confidence_scores.append(transcript['confidence'])
                    
                    avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
                    
                    return {
                        'success': True,
                        'text': transcription.strip(),
                        'confidence': avg_confidence,
                        'raw_result': result
                    }
                else:
                    return {
                        'success': False,
                        'error': '识别结果为空',
                        'text': '',
                        'confidence': 0
                    }
            else:
                return {
                    'success': False,
                    'error': f'API调用失败: {recognition.message}',
                    'text': '',
                    'confidence': 0
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'语音识别失败: {str(e)}',
                'text': '',
                'confidence': 0
            }
    
    def process_video_transcription(self, video_url: str) -> Dict[str, Any]:
        """处理视频转录的完整流程"""
        temp_files = []
        
        try:
            # 方法1: 直接从视频URL识别（推荐）
            result = self.recognize_speech_from_url(video_url)
            if result['success']:
                return result
            
            # 方法2: 下载视频后提取音频再识别
            print("直接识别失败，尝试下载视频...")
            
            # 下载视频
            video_path = self.download_video(video_url)
            temp_files.append(video_path)
            
            # 提取音频
            audio_path = self.extract_audio_from_video(video_path)
            temp_files.append(audio_path)
            
            # 识别音频
            result = self.recognize_speech_from_file(audio_path)
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': f'视频转录处理失败: {str(e)}',
                'text': '',
                'confidence': 0
            }
        finally:
            # 清理临时文件
            for temp_file in temp_files:
                try:
                    if os.path.exists(temp_file):
                        os.unlink(temp_file)
                except:
                    pass
