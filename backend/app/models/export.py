from datetime import datetime
from enum import Enum
from app import db

class ExportType(Enum):
    """导出类型枚举"""
    EXCEL = "excel"
    NOTION = "notion"
    EMAIL = "email"

class ExportStatus(Enum):
    """导出状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class ExportRecord(db.Model):
    """导出记录模型"""
    __tablename__ = 'export_records'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    
    # 导出基本信息
    export_type = db.Column(db.Enum(ExportType), nullable=False, index=True)
    status = db.Column(db.Enum(ExportStatus), default=ExportStatus.PENDING, index=True)
    
    # 导出配置
    video_ids = db.Column(db.Text)  # JSON格式的视频ID列表
    export_config = db.Column(db.Text)  # JSON格式的导出配置
    
    # 导出结果
    file_path = db.Column(db.String(500))  # 导出文件路径（Excel）
    file_url = db.Column(db.String(500))  # 文件下载链接
    notion_page_url = db.Column(db.String(500))  # Notion页面链接
    email_recipients = db.Column(db.Text)  # 邮件收件人列表（JSON格式）
    
    # 处理信息
    task_id = db.Column(db.String(255), index=True)  # Celery任务ID
    progress = db.Column(db.Integer, default=0)  # 进度百分比
    error_message = db.Column(db.Text)  # 错误信息
    
    # 统计信息
    total_videos = db.Column(db.Integer, default=0)  # 总视频数
    processed_videos = db.Column(db.Integer, default=0)  # 已处理视频数
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    
    def start_export(self, task_id):
        """开始导出"""
        self.status = ExportStatus.PROCESSING
        self.task_id = task_id
        self.started_at = datetime.utcnow()
        db.session.commit()
    
    def update_progress(self, progress, processed_videos=None):
        """更新进度"""
        self.progress = progress
        if processed_videos is not None:
            self.processed_videos = processed_videos
        db.session.commit()
    
    def complete_export(self, **kwargs):
        """完成导出"""
        self.status = ExportStatus.COMPLETED
        self.completed_at = datetime.utcnow()
        self.progress = 100
        
        # 更新结果信息
        if 'file_path' in kwargs:
            self.file_path = kwargs['file_path']
        if 'file_url' in kwargs:
            self.file_url = kwargs['file_url']
        if 'notion_page_url' in kwargs:
            self.notion_page_url = kwargs['notion_page_url']
        
        self.error_message = None
        db.session.commit()
    
    def fail_export(self, error_message):
        """导出失败"""
        self.status = ExportStatus.FAILED
        self.completed_at = datetime.utcnow()
        self.error_message = error_message
        db.session.commit()
    
    @property
    def export_duration(self):
        """导出耗时（秒）"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'export_type': self.export_type.value,
            'status': self.status.value,
            'video_ids': self.video_ids,
            'export_config': self.export_config,
            'file_path': self.file_path,
            'file_url': self.file_url,
            'notion_page_url': self.notion_page_url,
            'email_recipients': self.email_recipients,
            'task_id': self.task_id,
            'progress': self.progress,
            'error_message': self.error_message,
            'total_videos': self.total_videos,
            'processed_videos': self.processed_videos,
            'export_duration': self.export_duration,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }
    
    def __repr__(self):
        return f'<ExportRecord {self.id}: {self.export_type.value} - {self.status.value}>'
