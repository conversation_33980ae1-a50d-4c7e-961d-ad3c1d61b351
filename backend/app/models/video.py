from datetime import datetime
from enum import Enum
from app import db

class ProcessingStatus(Enum):
    """处理状态枚举"""
    PENDING = "pending"          # 等待处理
    PROCESSING = "processing"    # 处理中
    COMPLETED = "completed"      # 处理完成
    FAILED = "failed"           # 处理失败
    CANCELLED = "cancelled"      # 已取消

class Video(db.Model):
    """视频记录模型"""
    __tablename__ = 'videos'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    
    # 视频基本信息
    video_id = db.Column(db.String(100), nullable=False, index=True)  # 抖音视频ID
    title = db.Column(db.String(500), nullable=False)  # 视频标题
    description = db.Column(db.Text)  # 视频描述
    original_url = db.Column(db.String(500), nullable=False)  # 原始分享链接
    video_url = db.Column(db.String(500))  # 无水印视频链接
    
    # 处理状态
    status = db.Column(db.Enum(ProcessingStatus), default=ProcessingStatus.PENDING, index=True)
    
    # 识别结果
    transcription_text = db.Column(db.Text)  # 转录文本
    transcription_confidence = db.Column(db.Float)  # 识别置信度
    
    # 处理信息
    processing_started_at = db.Column(db.DateTime)
    processing_completed_at = db.Column(db.DateTime)
    error_message = db.Column(db.Text)  # 错误信息
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    processing_tasks = db.relationship('VideoProcessingTask', backref='video', lazy='dynamic', cascade='all, delete-orphan')
    
    def start_processing(self):
        """开始处理"""
        self.status = ProcessingStatus.PROCESSING
        self.processing_started_at = datetime.utcnow()
        db.session.commit()
    
    def complete_processing(self, transcription_text, confidence=None):
        """完成处理"""
        self.status = ProcessingStatus.COMPLETED
        self.processing_completed_at = datetime.utcnow()
        self.transcription_text = transcription_text
        self.transcription_confidence = confidence
        self.error_message = None
        db.session.commit()
    
    def fail_processing(self, error_message):
        """处理失败"""
        self.status = ProcessingStatus.FAILED
        self.processing_completed_at = datetime.utcnow()
        self.error_message = error_message
        db.session.commit()
    
    def cancel_processing(self):
        """取消处理"""
        self.status = ProcessingStatus.CANCELLED
        self.processing_completed_at = datetime.utcnow()
        db.session.commit()
    
    @property
    def processing_duration(self):
        """处理耗时（秒）"""
        if self.processing_started_at and self.processing_completed_at:
            return (self.processing_completed_at - self.processing_started_at).total_seconds()
        return None
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'video_id': self.video_id,
            'title': self.title,
            'description': self.description,
            'original_url': self.original_url,
            'video_url': self.video_url,
            'status': self.status.value,
            'transcription_text': self.transcription_text,
            'transcription_confidence': self.transcription_confidence,
            'processing_started_at': self.processing_started_at.isoformat() if self.processing_started_at else None,
            'processing_completed_at': self.processing_completed_at.isoformat() if self.processing_completed_at else None,
            'processing_duration': self.processing_duration,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<Video {self.video_id}: {self.title[:50]}>'

class VideoProcessingTask(db.Model):
    """视频处理任务模型（用于Celery任务跟踪）"""
    __tablename__ = 'video_processing_tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    video_id = db.Column(db.Integer, db.ForeignKey('videos.id'), nullable=False, index=True)
    
    # 任务信息
    task_id = db.Column(db.String(255), unique=True, nullable=False, index=True)  # Celery任务ID
    task_name = db.Column(db.String(100), nullable=False)  # 任务名称
    status = db.Column(db.String(50), default='PENDING')  # 任务状态
    
    # 进度信息
    progress = db.Column(db.Integer, default=0)  # 进度百分比
    current_step = db.Column(db.String(200))  # 当前步骤描述
    
    # 结果信息
    result = db.Column(db.Text)  # 任务结果（JSON格式）
    error_info = db.Column(db.Text)  # 错误信息
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'video_id': self.video_id,
            'task_id': self.task_id,
            'task_name': self.task_name,
            'status': self.status,
            'progress': self.progress,
            'current_step': self.current_step,
            'result': self.result,
            'error_info': self.error_info,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }
    
    def __repr__(self):
        return f'<VideoProcessingTask {self.task_id}: {self.task_name}>'
