from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models import Video, VideoProcessingTask, ProcessingStatus
from app.services import DouyinService
from app.tasks import process_video_task, batch_process_videos_task
from sqlalchemy import desc
import json

bp = Blueprint('videos', __name__)

@bp.route('/validate', methods=['POST'])
@jwt_required()
def validate_urls():
    """验证抖音链接格式"""
    try:
        data = request.get_json()
        urls_text = data.get('urls', '').strip()
        
        if not urls_text:
            return jsonify({'error': '请输入视频链接'}), 400
        
        douyin_service = DouyinService()
        
        # 提取所有链接
        urls = douyin_service.extract_urls_from_text(urls_text)
        
        if not urls:
            return jsonify({'error': '未找到有效的抖音链接'}), 400
        
        # 验证每个链接
        valid_urls = []
        invalid_urls = []
        
        for url in urls:
            if douyin_service.validate_douyin_url(url):
                valid_urls.append(url)
            else:
                invalid_urls.append(url)
        
        return jsonify({
            'valid_urls': valid_urls,
            'invalid_urls': invalid_urls,
            'total_count': len(urls),
            'valid_count': len(valid_urls),
            'invalid_count': len(invalid_urls)
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'链接验证失败: {str(e)}'}), 500

@bp.route('/submit', methods=['POST'])
@jwt_required()
def submit_videos():
    """提交视频处理任务"""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        urls = data.get('urls', [])
        if not urls:
            return jsonify({'error': '请提供视频链接'}), 400
        
        douyin_service = DouyinService()
        created_videos = []
        failed_urls = []
        
        # 批量解析视频信息
        for url in urls:
            try:
                # 检查是否已存在相同的视频
                existing_video = Video.query.filter_by(
                    user_id=current_user_id,
                    original_url=url
                ).first()
                
                if existing_video:
                    # 如果视频已存在且未完成，重新处理
                    if existing_video.status in [ProcessingStatus.FAILED, ProcessingStatus.CANCELLED]:
                        existing_video.status = ProcessingStatus.PENDING
                        existing_video.error_message = None
                        db.session.commit()
                        created_videos.append(existing_video)
                    continue
                
                # 解析视频信息
                video_info = douyin_service.parse_share_url(url)
                
                # 创建视频记录
                video = Video(
                    user_id=current_user_id,
                    video_id=video_info['video_id'],
                    title=video_info['title'],
                    description=video_info.get('description', ''),
                    original_url=url,
                    video_url=video_info.get('video_url'),
                    status=ProcessingStatus.PENDING
                )
                
                db.session.add(video)
                db.session.flush()  # 获取ID但不提交
                created_videos.append(video)
                
            except Exception as e:
                failed_urls.append({
                    'url': url,
                    'error': str(e)
                })
        
        db.session.commit()
        
        # 启动异步处理任务
        video_ids = [video.id for video in created_videos]
        if video_ids:
            if len(video_ids) == 1:
                # 单个视频处理
                task = process_video_task.delay(video_ids[0])
            else:
                # 批量视频处理
                task = batch_process_videos_task.delay(video_ids)
        
        return jsonify({
            'message': f'成功提交 {len(created_videos)} 个视频处理任务',
            'created_videos': [video.to_dict() for video in created_videos],
            'failed_urls': failed_urls,
            'task_id': task.id if video_ids else None
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'提交任务失败: {str(e)}'}), 500

@bp.route('/list', methods=['GET'])
@jwt_required()
def list_videos():
    """获取用户的视频列表"""
    try:
        current_user_id = get_jwt_identity()
        
        # 分页参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        # 过滤参数
        status = request.args.get('status')
        search = request.args.get('search', '').strip()
        
        # 构建查询
        query = Video.query.filter_by(user_id=current_user_id)
        
        if status:
            try:
                status_enum = ProcessingStatus(status)
                query = query.filter_by(status=status_enum)
            except ValueError:
                return jsonify({'error': '无效的状态参数'}), 400
        
        if search:
            query = query.filter(
                (Video.title.contains(search)) |
                (Video.description.contains(search)) |
                (Video.transcription_text.contains(search))
            )
        
        # 排序和分页
        query = query.order_by(desc(Video.created_at))
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        videos = pagination.items
        
        return jsonify({
            'videos': [video.to_dict() for video in videos],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取视频列表失败: {str(e)}'}), 500

@bp.route('/<int:video_id>', methods=['GET'])
@jwt_required()
def get_video(video_id):
    """获取单个视频详情"""
    try:
        current_user_id = get_jwt_identity()
        
        video = Video.query.filter_by(
            id=video_id,
            user_id=current_user_id
        ).first()
        
        if not video:
            return jsonify({'error': '视频不存在'}), 404
        
        return jsonify({'video': video.to_dict()}), 200
        
    except Exception as e:
        return jsonify({'error': f'获取视频详情失败: {str(e)}'}), 500

@bp.route('/<int:video_id>', methods=['DELETE'])
@jwt_required()
def delete_video(video_id):
    """删除视频记录"""
    try:
        current_user_id = get_jwt_identity()
        
        video = Video.query.filter_by(
            id=video_id,
            user_id=current_user_id
        ).first()
        
        if not video:
            return jsonify({'error': '视频不存在'}), 404
        
        # 如果正在处理中，先取消处理
        if video.status == ProcessingStatus.PROCESSING:
            video.cancel_processing()
        
        db.session.delete(video)
        db.session.commit()
        
        return jsonify({'message': '视频删除成功'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'删除视频失败: {str(e)}'}), 500

@bp.route('/<int:video_id>/reprocess', methods=['POST'])
@jwt_required()
def reprocess_video(video_id):
    """重新处理视频"""
    try:
        current_user_id = get_jwt_identity()
        
        video = Video.query.filter_by(
            id=video_id,
            user_id=current_user_id
        ).first()
        
        if not video:
            return jsonify({'error': '视频不存在'}), 404
        
        if video.status == ProcessingStatus.PROCESSING:
            return jsonify({'error': '视频正在处理中'}), 400
        
        # 重置状态
        video.status = ProcessingStatus.PENDING
        video.error_message = None
        video.transcription_text = None
        video.transcription_confidence = None
        video.processing_started_at = None
        video.processing_completed_at = None
        
        db.session.commit()
        
        # 启动处理任务
        task = process_video_task.delay(video_id)
        
        return jsonify({
            'message': '重新处理任务已启动',
            'task_id': task.id,
            'video': video.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'重新处理失败: {str(e)}'}), 500

@bp.route('/task/<task_id>/status', methods=['GET'])
@jwt_required()
def get_task_status(task_id):
    """获取任务状态"""
    try:
        from app import celery
        
        # 获取Celery任务状态
        task = celery.AsyncResult(task_id)
        
        # 获取数据库中的任务记录
        task_record = VideoProcessingTask.query.filter_by(task_id=task_id).first()
        
        response = {
            'task_id': task_id,
            'status': task.status,
            'result': task.result if task.successful() else None,
            'error': str(task.result) if task.failed() else None
        }
        
        if task_record:
            response.update({
                'progress': task_record.progress,
                'current_step': task_record.current_step,
                'video_id': task_record.video_id
            })
        
        return jsonify(response), 200
        
    except Exception as e:
        return jsonify({'error': f'获取任务状态失败: {str(e)}'}), 500

@bp.route('/statistics', methods=['GET'])
@jwt_required()
def get_statistics():
    """获取用户视频统计信息"""
    try:
        current_user_id = get_jwt_identity()
        
        # 统计各状态的视频数量
        stats = {}
        for status in ProcessingStatus:
            count = Video.query.filter_by(
                user_id=current_user_id,
                status=status
            ).count()
            stats[status.value] = count
        
        # 总数
        total_count = Video.query.filter_by(user_id=current_user_id).count()
        
        # 最近处理的视频
        recent_videos = Video.query.filter_by(
            user_id=current_user_id
        ).order_by(desc(Video.created_at)).limit(5).all()
        
        return jsonify({
            'statistics': stats,
            'total_count': total_count,
            'recent_videos': [video.to_dict() for video in recent_videos]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取统计信息失败: {str(e)}'}), 500
