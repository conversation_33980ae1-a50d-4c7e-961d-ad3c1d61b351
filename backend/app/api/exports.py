from flask import Blueprint, request, jsonify, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models import Video, User, ExportRecord, ExportType, ExportStatus, ProcessingStatus
from app.services import ExportService
from app.tasks import export_task
import json
import os

bp = Blueprint('exports', __name__)

@bp.route('/excel', methods=['POST'])
@jwt_required()
def export_excel():
    """导出Excel文件"""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        video_ids = data.get('video_ids', [])
        if not video_ids:
            return jsonify({'error': '请选择要导出的视频'}), 400
        
        # 获取用户的视频
        videos = Video.query.filter(
            Video.id.in_(video_ids),
            Video.user_id == current_user_id,
            Video.status == ProcessingStatus.COMPLETED
        ).all()
        
        if not videos:
            return jsonify({'error': '没有找到可导出的视频'}), 400
        
        # 创建导出记录
        export_record = ExportRecord(
            user_id=current_user_id,
            export_type=ExportType.EXCEL,
            video_ids=json.dumps(video_ids),
            total_videos=len(videos)
        )
        db.session.add(export_record)
        db.session.commit()
        
        # 启动异步导出任务
        task = export_task.delay(export_record.id)
        export_record.start_export(task.id)
        
        return jsonify({
            'message': 'Excel导出任务已启动',
            'export_id': export_record.id,
            'task_id': task.id
        }), 202
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'导出失败: {str(e)}'}), 500

@bp.route('/notion', methods=['POST'])
@jwt_required()
def sync_notion():
    """同步到Notion"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user.notion_token or not user.notion_database_id:
            return jsonify({'error': '请先配置Notion集成信息'}), 400
        
        data = request.get_json()
        video_ids = data.get('video_ids', [])
        
        if not video_ids:
            return jsonify({'error': '请选择要同步的视频'}), 400
        
        # 获取用户的视频
        videos = Video.query.filter(
            Video.id.in_(video_ids),
            Video.user_id == current_user_id,
            Video.status == ProcessingStatus.COMPLETED
        ).all()
        
        if not videos:
            return jsonify({'error': '没有找到可同步的视频'}), 400
        
        # 创建导出记录
        export_record = ExportRecord(
            user_id=current_user_id,
            export_type=ExportType.NOTION,
            video_ids=json.dumps(video_ids),
            total_videos=len(videos)
        )
        db.session.add(export_record)
        db.session.commit()
        
        # 启动异步同步任务
        task = export_task.delay(export_record.id)
        export_record.start_export(task.id)
        
        return jsonify({
            'message': 'Notion同步任务已启动',
            'export_id': export_record.id,
            'task_id': task.id
        }), 202
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'同步失败: {str(e)}'}), 500

@bp.route('/email', methods=['POST'])
@jwt_required()
def send_email():
    """发送邮件"""
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()
        
        video_ids = data.get('video_ids', [])
        recipients = data.get('recipients', [])
        include_excel = data.get('include_excel', True)
        
        if not video_ids:
            return jsonify({'error': '请选择要发送的视频'}), 400
        
        if not recipients:
            return jsonify({'error': '请提供收件人邮箱'}), 400
        
        # 验证邮箱格式
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        for email in recipients:
            if not re.match(email_pattern, email):
                return jsonify({'error': f'邮箱格式不正确: {email}'}), 400
        
        # 获取用户的视频
        videos = Video.query.filter(
            Video.id.in_(video_ids),
            Video.user_id == current_user_id,
            Video.status == ProcessingStatus.COMPLETED
        ).all()
        
        if not videos:
            return jsonify({'error': '没有找到可发送的视频'}), 400
        
        # 创建导出记录
        export_record = ExportRecord(
            user_id=current_user_id,
            export_type=ExportType.EMAIL,
            video_ids=json.dumps(video_ids),
            email_recipients=json.dumps(recipients),
            export_config=json.dumps({'include_excel': include_excel}),
            total_videos=len(videos)
        )
        db.session.add(export_record)
        db.session.commit()
        
        # 启动异步邮件任务
        task = export_task.delay(export_record.id)
        export_record.start_export(task.id)
        
        return jsonify({
            'message': '邮件发送任务已启动',
            'export_id': export_record.id,
            'task_id': task.id
        }), 202
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'邮件发送失败: {str(e)}'}), 500

@bp.route('/records', methods=['GET'])
@jwt_required()
def get_export_records():
    """获取导出记录"""
    try:
        current_user_id = get_jwt_identity()
        
        # 分页参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        # 过滤参数
        export_type = request.args.get('type')
        status = request.args.get('status')
        
        # 构建查询
        query = ExportRecord.query.filter_by(user_id=current_user_id)
        
        if export_type:
            try:
                type_enum = ExportType(export_type)
                query = query.filter_by(export_type=type_enum)
            except ValueError:
                return jsonify({'error': '无效的导出类型'}), 400
        
        if status:
            try:
                status_enum = ExportStatus(status)
                query = query.filter_by(status=status_enum)
            except ValueError:
                return jsonify({'error': '无效的状态参数'}), 400
        
        # 排序和分页
        query = query.order_by(ExportRecord.created_at.desc())
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        records = pagination.items
        
        return jsonify({
            'records': [record.to_dict() for record in records],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'获取导出记录失败: {str(e)}'}), 500

@bp.route('/records/<int:export_id>/status', methods=['GET'])
@jwt_required()
def get_export_status(export_id):
    """获取导出状态"""
    try:
        current_user_id = get_jwt_identity()
        
        export_record = ExportRecord.query.filter_by(
            id=export_id,
            user_id=current_user_id
        ).first()
        
        if not export_record:
            return jsonify({'error': '导出记录不存在'}), 404
        
        # 如果有任务ID，获取Celery任务状态
        task_status = None
        if export_record.task_id:
            from app import celery
            task = celery.AsyncResult(export_record.task_id)
            task_status = {
                'status': task.status,
                'result': task.result if task.successful() else None,
                'error': str(task.result) if task.failed() else None
            }
        
        response = export_record.to_dict()
        if task_status:
            response['task_status'] = task_status
        
        return jsonify(response), 200
        
    except Exception as e:
        return jsonify({'error': f'获取导出状态失败: {str(e)}'}), 500

@bp.route('/download/<filename>', methods=['GET'])
@jwt_required()
def download_file(filename):
    """下载导出文件"""
    try:
        current_user_id = get_jwt_identity()
        
        # 验证文件是否属于当前用户
        export_record = ExportRecord.query.filter(
            ExportRecord.user_id == current_user_id,
            ExportRecord.file_path.contains(filename)
        ).first()
        
        if not export_record:
            return jsonify({'error': '文件不存在或无权限访问'}), 404
        
        file_path = export_record.file_path
        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404
        
        return send_file(
            file_path,
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        return jsonify({'error': f'文件下载失败: {str(e)}'}), 500

@bp.route('/records/<int:export_id>', methods=['DELETE'])
@jwt_required()
def delete_export_record(export_id):
    """删除导出记录"""
    try:
        current_user_id = get_jwt_identity()
        
        export_record = ExportRecord.query.filter_by(
            id=export_id,
            user_id=current_user_id
        ).first()
        
        if not export_record:
            return jsonify({'error': '导出记录不存在'}), 404
        
        # 删除关联的文件
        if export_record.file_path and os.path.exists(export_record.file_path):
            try:
                os.unlink(export_record.file_path)
            except:
                pass
        
        db.session.delete(export_record)
        db.session.commit()
        
        return jsonify({'message': '导出记录删除成功'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'删除导出记录失败: {str(e)}'}), 500
