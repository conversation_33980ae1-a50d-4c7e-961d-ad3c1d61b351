version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: douyin_mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: douyin_extract
      MYSQL_USER: douyin_user
      MYSQL_PASSWORD: douyin_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - douyin_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: douyin_redis
    ports:
      - "6379:6379"
    networks:
      - douyin_network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: douyin_backend
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=mysql://douyin_user:douyin_password@mysql:3306/douyin_extract
      - REDIS_URL=redis://redis:6379/0
    ports:
      - "5000:5000"
    depends_on:
      - mysql
      - redis
    volumes:
      - ./backend:/app
      - uploads_data:/app/uploads
    networks:
      - douyin_network

  # Celery异步任务处理
  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: douyin_celery
    command: celery -A run.celery worker --loglevel=info
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=mysql://douyin_user:douyin_password@mysql:3306/douyin_extract
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - mysql
      - redis
    volumes:
      - ./backend:/app
      - uploads_data:/app/uploads
    networks:
      - douyin_network

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: douyin_frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - douyin_network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: douyin_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - frontend
      - backend
    networks:
      - douyin_network

volumes:
  mysql_data:
  uploads_data:

networks:
  douyin_network:
    driver: bridge
