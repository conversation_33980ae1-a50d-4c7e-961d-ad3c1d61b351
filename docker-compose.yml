version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: quickvidnote_postgres
    environment:
      POSTGRES_DB: quickvidnote_extract
      POSTGRES_USER: quickvidnote_user
      POSTGRES_PASSWORD: Y8mNkxKr10TEJz
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres-init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - quickvidnote_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: quickvidnote_redis
    ports:
      - "6379:6379"
    networks:
      - quickvidnote_network

  # # 后端API服务
  # backend:
  #   build:
  #     context: ./backend
  #     dockerfile: Dockerfile
  #   container_name: quickvidnote_backend
  #   environment:
  #     - FLASK_ENV=production
  #     - DATABASE_URL=***********************************************************/quickvidnote_extract
  #     - REDIS_URL=redis://redis:6379/0
  #   ports:
  #     - "5000:5000"
  #   depends_on:
  #     - postgres
  #     - redis
  #   volumes:
  #     - ./backend:/app
  #     - uploads_data:/app/uploads
  #   networks:
  #     - quickvidnote_network

  # Celery异步任务处理
  # celery:
  #   build:
  #     context: ./backend
  #     dockerfile: Dockerfile
  #   container_name: quickvidnote_celery
  #   command: celery -A run.celery worker --loglevel=info
  #   environment:
  #     - FLASK_ENV=production
  #     - DATABASE_URL=***********************************************************/quickvidnote_extract
  #     - REDIS_URL=redis://redis:6379/0
  #   depends_on:
  #     - postgres
  #     - redis
  #   volumes:
  #     - ./backend:/app
  #     - uploads_data:/app/uploads
  #   networks:
  #     - quickvidnote_network

  # # 前端服务
  # frontend:
  #   build:
  #     context: ./frontend
  #     dockerfile: Dockerfile
  #   container_name: quickvidnote_frontend
  #   ports:
  #     - "3000:3000"
  #   depends_on:
  #     - backend
  #   networks:
  #     - quickvidnote_network

  # # Nginx反向代理
  # nginx:
  #   image: nginx:alpine
  #   container_name: quickvidnote_nginx
  #   ports:
  #     - "80:80"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf
  #   depends_on:
  #     - frontend
  #     - backend
  #   networks:
  #     - quickvidnote_network

volumes:
  postgres_data:
  uploads_data:

networks:
  quickvidnote_network:
    driver: bridge
