-- PostgreSQL初始化脚本
-- 创建数据库和用户（如果不存在）

-- 创建数据库
CREATE DATABASE quickvidnote_extract;

-- 创建用户
CREATE USER quickvidnote_user WITH PASSWORD 'Y8mNkxKr10TEJz';

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE quickvidnote_extract TO quickvidnote_user;

-- 切换到目标数据库
\c quickvidnote_extract;

-- 授予schema权限
GRANT ALL ON SCHEMA public TO quickvidnote_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO quickvidnote_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO quickvidnote_user;
