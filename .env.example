# Flask配置
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/quickvidnote_extract
# 或者使用MySQL
# DATABASE_URL=mysql://username:password@localhost:3306/quickvidnote_extract

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 阿里云语音识别配置
ALIYUN_ACCESS_KEY_ID=your-aliyun-access-key-id
ALIYUN_ACCESS_KEY_SECRET=your-aliyun-access-key-secret
ALIYUN_REGION=cn-shanghai

# 邮件配置
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_DEFAULT_SENDER=<EMAIL>

# Notion配置
NOTION_TOKEN=your-notion-integration-token
NOTION_DATABASE_ID=your-notion-database-id

# 文件上传配置
UPLOAD_FOLDER=uploads
PORT=5000
