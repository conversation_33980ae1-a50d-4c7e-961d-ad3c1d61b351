# 数据库迁移指南

本项目已从MySQL切换到PostgreSQL作为主要数据库。以下是迁移和使用指南。

## 数据库变更说明

### 主要变更
- 默认数据库从MySQL 8.0切换到PostgreSQL 15
- 更新了所有相关配置文件
- 移除了MySQL相关依赖，保留PostgreSQL依赖
- 更新了Docker配置

### 文件变更列表
- `backend/requirements.txt` - 移除mysql-connector-python
- `backend/app/config.py` - 更新默认数据库URL
- `backend/Dockerfile` - 替换MySQL开发包为PostgreSQL开发包
- `docker-compose.yml` - 替换MySQL服务为PostgreSQL服务
- `.env.example` - 更新数据库连接示例

## 本地开发环境设置

### 1. 使用Docker（推荐）

```bash
# 启动所有服务（包括PostgreSQL）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看PostgreSQL日志
docker-compose logs postgres
```

### 2. 本地PostgreSQL安装

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### macOS
```bash
brew install postgresql
brew services start postgresql
```

#### Windows
下载并安装PostgreSQL官方安装包：https://www.postgresql.org/download/windows/

### 3. 创建数据库和用户

```bash
# 连接到PostgreSQL
sudo -u postgres psql

# 在PostgreSQL命令行中执行
CREATE DATABASE quickvidnote_extract;
CREATE USER quickvidnote_user WITH PASSWORD 'Y8mNkxKr10TEJz';
GRANT ALL PRIVILEGES ON DATABASE quickvidnote_extract TO quickvidnote_user;
\q
```

### 4. 配置环境变量

复制并编辑环境变量文件：
```bash
cp .env.example .env
```

确保数据库URL正确：
```
DATABASE_URL=postgresql://quickvidnote_user:Y8mNkxKr10TEJz@localhost:5432/quickvidnote_extract
```

### 5. 初始化数据库

```bash
cd backend

# 安装依赖
pip install -r requirements.txt

# 初始化数据库和创建管理员用户
python init_db.py all

# 或者分步执行
python init_db.py init   # 初始化数据库表
python init_db.py admin  # 创建管理员用户
```

## 数据迁移（从MySQL到PostgreSQL）

如果你之前使用MySQL并需要迁移数据，可以使用以下工具：

### 1. 使用pgloader（推荐）

```bash
# 安装pgloader
sudo apt install pgloader  # Ubuntu/Debian
brew install pgloader       # macOS

# 迁移数据
pgloader mysql://old_user:old_password@localhost/old_db_name \
         postgresql://quickvidnote_user:Y8mNkxKr10TEJz@localhost/quickvidnote_extract
```

### 2. 手动导出导入

```bash
# 从MySQL导出数据
mysqldump -u username -p database_name > backup.sql

# 转换SQL语法（可能需要手动调整）
# 然后导入到PostgreSQL
psql -U quickvidnote_user -d quickvidnote_extract -f converted_backup.sql
```

## 生产环境部署

### 1. 环境变量配置

确保生产环境中设置正确的数据库连接：
```bash
export DATABASE_URL=****************************************/database_name
```

### 2. 数据库优化

在生产环境中，建议对PostgreSQL进行以下优化：

```sql
-- 调整PostgreSQL配置（postgresql.conf）
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
```

### 3. 备份策略

```bash
# 创建备份
pg_dump -U quickvidnote_user -h localhost quickvidnote_extract > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复备份
psql -U quickvidnote_user -h localhost quickvidnote_extract < backup_file.sql
```

## 常见问题

### 1. 连接错误
- 检查PostgreSQL服务是否运行
- 验证用户名、密码和数据库名
- 确认防火墙设置

### 2. 权限问题
```sql
-- 授予用户所有权限
GRANT ALL PRIVILEGES ON DATABASE quickvidnote_extract TO quickvidnote_user;
GRANT ALL ON SCHEMA public TO quickvidnote_user;
```

### 3. 字符编码问题
确保数据库使用UTF-8编码：
```sql
CREATE DATABASE quickvidnote_extract WITH ENCODING 'UTF8';
```

## 性能监控

### 1. 查看活动连接
```sql
SELECT * FROM pg_stat_activity;
```

### 2. 查看数据库大小
```sql
SELECT pg_size_pretty(pg_database_size('quickvidnote_extract'));
```

### 3. 查看表大小
```sql
SELECT schemaname,tablename,attname,n_distinct,correlation 
FROM pg_stats 
WHERE schemaname = 'public';
```

## 支持

如果在数据库迁移过程中遇到问题，请：
1. 检查日志文件
2. 验证配置文件
3. 确认网络连接
4. 查看PostgreSQL官方文档
