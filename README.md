# 抖音视频文本提取系统

一个前后端分离的Web应用，用于提取抖音视频中的文本内容。

## 项目架构

```
douyin-extract/
├── backend/                 # 后端Python服务
│   ├── app/
│   │   ├── __init__.py
│   │   ├── models/         # 数据模型
│   │   ├── api/           # API路由
│   │   ├── services/      # 业务逻辑
│   │   ├── utils/         # 工具函数
│   │   └── config.py      # 配置文件
│   ├── requirements.txt
│   ├── run.py
│   └── README.md
├── frontend/               # 前端React应用
│   ├── public/
│   ├── src/
│   │   ├── components/    # 组件
│   │   ├── pages/        # 页面
│   │   ├── services/     # API服务
│   │   ├── utils/        # 工具函数
│   │   └── App.js
│   ├── package.json
│   └── README.md
├── docker-compose.yml      # Docker编排
├── .env.example           # 环境变量示例
└── README.md
```

## 功能特性

### 前端功能
- 用户登录/注册
- 抖音视频链接输入（单个或批量）
- 实时处理进度显示
- 历史记录查看
- 结果导出（Excel、Notion同步、邮件发送）

### 后端功能
- 用户认证与授权
- 抖音视频链接验证与解析
- 视频信息提取
- 语音转文字处理
- 数据存储与管理
- 导出功能API

## 技术栈

### 后端
- Python 3.8+
- Flask/FastAPI
- SQLAlchemy (ORM)
- MySQL/PostgreSQL
- 阿里云语音识别API
- Celery (异步任务处理)

### 前端
- React 18
- Ant Design / Material-UI
- Axios (HTTP客户端)
- React Router
- Redux/Context API

### 部署
- Docker & Docker Compose
- Nginx (反向代理)

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- MySQL/PostgreSQL
- Redis (用于Celery)

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd douyin-extract
```

2. 后端设置
```bash
cd backend
pip install -r requirements.txt
cp .env.example .env
# 编辑 .env 文件配置数据库和API密钥
python run.py
```

3. 前端设置
```bash
cd frontend
npm install
npm start
```

4. 使用Docker (推荐)
```bash
docker-compose up -d
```

## API文档

详细的API文档将在后端启动后通过 `/docs` 端点提供。

## 许可证

MIT License
